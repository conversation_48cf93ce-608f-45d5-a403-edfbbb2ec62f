@charset "utf-8";
/* CSS Document */
*{
font-family:宋体, Helvetica, sans-serif;
font-size:12px;
}

.topnav a:ACTIVE{color: red;}
#fullbg{
background-color: Gray;
display:none;
z-index:3;
position:absolute;
left:0px;
top:0px;
filter:Alpha(Opacity=30);
/* IE */
-moz-opacity:0.4; 
/* Moz + FF */
opacity: 0.4; 
}

#dialog {
position:absolute;
width:200px;
height:200px;
display: none;
z-index: 5;
}

tr.over td { 
  background:#EDD8B4;   
  cursor:pointer; 
} 
.winclass {
	font-family: "宋体";
	font-size: 14px;
	font-weight: bold;
	color: #5F5F5F;
}

.bigbox{ margin:0 auto;width:100%}
.header{ background:url(../images/logobj_03.jpg) repeat-x left top; height:85px;}
.topnav{ float:right; overflow:hidden; line-height:16px; height:20px; margin:8px;}
.red_color{ color:#e90000;}
.personal{ background:url(../images/user_business.png) no-repeat left center; padding-left:20px;display:inline-block;}
.department{ background:url(../images/my-account.png) no-repeat left center; padding-left:20px; display:inline-block;}
/*group.png   company*/
#gantanhao{background-repeat:no-repeat;padding-left:20px; display:inline-block;}
.group{ background:url(../images/current-work.png) no-repeat left center; padding-left:20px; display:inline-block;}
.help{ background:url(../images/help.png) no-repeat left center; padding-left:20px; display:inline-block;}
.refresh{ background:url(../images/refresh.png) no-repeat left center; padding-left:20px; display:inline-block;}
.exit{ background:url(../images/exit.png) no-repeat left center; padding-left:20px; display:inline-block;}


.red_mainnav{ position:absolute; top:57px;  height:28px; display:inline-block; width:1200px;}
.red_mainnav ul{ float:left;}
.red_mainnav ul li{ background:url(../images/red_nav_06.gif) no-repeat -90px top; width:90px; height:25px; padding-top:10px; float:left; margin-right:4px; text-align:center;}
.red_mainnav ul li a{ display:block;}
.red_mainnav ul li a:hover{ text-decoration:none;}
.red_mainnav ul li.select{ background:url(../images/red_nav_06.jpg) no-repeat left top; width:90px; height:20px;}
.red_mainnav ul li.select1{ background:url(../images/red_nav_07.jpg) repeat-x left top; width:400px; height:20px; padding-top:10px; float:left; margin-right:4px; text-align:center;}
.red_mainnav ul li.select2{ background:url(../images/red_nav_07.jpg) repeat-x left top; width:90px; height:20px; padding-top:10px; float:left; margin-right:4px; text-align:center;}
.red_mainnav ul li.select a{ color:#fff;}

.nav_line{ /* background:url(../images/subnav_10.jpg) repeat-x;height:2px; */ overflow:hidden;}
.notice_mes{ 
/* 	background:url(../images/subnav_12.jpg) repeat-x top;
	height:30px;  */
    background: linear-gradient(to bottom, #b8d1ff 0%, #e6f0ff 50%, #ffffff 100%) ;
    height: 40px ;
    width: 100% ;
    border-top: 1px solid #a3c2f5 ;
    border-bottom: 1px solid #e6eeff ;
    overflow: hidden; 
    margin-bottom: 8px;
	}
.noticeFont{
    font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
    font-size: 14px;
    color: #3D3E40;
    font-weight: normal;
    line-height: 40px;
    padding-left: 10px;
}
/* .notice_mes0{  height:30px; overflow:hidden; margin-bottom:0px;}
.notice_mes1{  height:30px;float: left; overflow:hidden; margin-bottom:-2px;width:652px;}
 */
.notice_mes2 ul li{  background:url(../images/red_nav_07.jpg) repeat-x top;width:80px; height:20px; padding-top:10px; float:left; text-align:center;  }
.notice_mes3{  margin-left: 75%;margin-top:-30px;height:30px;float: right; overflow:hidden; margin-bottom:-2px;width:27%;}

	
.notice{ background:url(../images/user.png) no-repeat left center; padding-left:20px; float:left;line-height:26px; position:relative; left:23px; zoom:1;}
.search{ 
	float:right; 
	position:relative; 
	/* top:4px;  */
	right:8px; 
	/* line-height:20px; */
	}
.search_nr{ border:1px solid #b1cad6; width:160px; height:18px; background-color:#fff; float:left; }
.search_btn{ background-color:#1891c8; width:50px; height:20px; border:none; float:left; color:#fff; font-family:Arial, Helvetica, sans-serif; font-weight:bold; cursor:pointer;}
.senior{ color:#0076ad; text-decoration:underline;}

/*.main_content{ position:relative; zoom:1; width:100%;}*/
.main_content{width:100%; height:100%;float:left;clear:both;}
/*.red_left{ border:1px solid #bbcfd8; width:250px; background-color:#FFFFFF; float:  left;overflow:auto;}*/
.red_left {
	border:1px solid #bbcfd8; 
	background-color: #FFFFFF;
	float: left;
	height: 100%;
	height:auto!important;
	width:19%;
	bottom: 0px;	 
	position:absolute!important;
    position:relative;
	top:125px!important;
	top:125px;
	overflow:hidden;
	padding:0;
}
/*.red_middle{ float:left; width:8px; height:611px;}*/
.red_middle{ 
	float: left;
	height: 100%;
	height:auto!important;
	width:8px;
	bottom: 0px;	 
	position:absolute!important;
    position:relative;
	top:125px!important;
	top:125px;
	overflow:hidden;
	margin-left:19%;}
.trm{
	margin-left:0;
}

.red_function{ background:url(../images/dd_15.gif) no-repeat 0; margin-bottom:10px;  height:31px;}
.quick{ height:20px; padding-left:34px; font-weight:bold; color:#fff;  position:relative; top:9px; *top:10px; top:11px\0; width:110px;}
.quick_fun{ height:auto;}
.quick_fun ul li{ line-height:30px; }
.quick_fun ul li:hover{ background-color:#9f9f9f;}
.quick_fun ul li a{background:url(../images/dd.png) no-repeat 20px center; padding-left:30px; display:block; height:30px;}
.quick_fun ul li a:hover{ color:#fff; text-decoration:none; background-color:#9f9f9f;}
.quick_set{border-top:1px solid #bbcfd8; border-bottom:1px solid #bbcfd8; text-align:right; height:28px; margin:10px auto;}
.quick_set a{ display:inline-block; margin:8px 8px auto auto;}

/*.red_right{ display:block; _top:10px; overflow:hidden;}*/
.red_right{
	float: left;
	margin-left: 20%;
	height: 100%;
	height:auto!important;
	width:79%;
	bottom: 0px;	 
	 position:absolute!important;
    position:relative;
	 top:125px!important;
	top:125px; 
	overflow:hidden;
	}
.trr{
	width:98%;
	margin-left:1%;
}

.yqy {
	float: left;
	height: 100%;
	width: 100%;
	overflow:scroll;
}
.column1,.column4{ border:1px solid #c0d2da; width:403px; background-color:#fff; float:left; overflow:hidden;  display: block;}
.column1{ height:280px; clear:both;}
.column_bt{ background:url(../images/column_18.gif) repeat-x 0 0; height:26px; line-height:26px;}
.zy_bt{ font-size:14px; font-weight:bold; float:left; margin-left:8px;}
.more{ float:right; color:#5d5d5d; margin-right:8px;}
.column_nr ul,.column_nr2 ul{ padding:5px;}
.column_nr ul li{ border-bottom:1px dashed #dadada;}
.column_nr ul li,.column_nr2 ul li,.list_Set ul li{ height:26px; line-height:26px; *height:25px; *line-height:25px; _height:25px; _line-height:25px; overflow:hidden;}
.nr_left{ width:308px; overflow:hidden; float:left; background:url(../images/middot_23.gif) no-repeat left center; padding-left:8px;}
.time{ width:68px; float:right;}

.flash_jpg{ width:315px; height:225px; float:left;}
.column2,.column4{ height:225px; float:left;}
.column2{ width:313px;}
.column2,.column3,.right_area,.list_tit,.column5,.column6,.column7{ border:1px solid #c0d2da; background-color:#fff; overflow:hidden; display: block;}
.column3{ width:638px; height:175px; float:left;}
.column5{ width:818px; float:left; height:175px;}
.column6{ width:638px; height:200px; float:left;}
.column7{ width:638px; height:225px; float:left;}
.red_mid{ width:640px; float:left; zoom:1;}
.right_area{ width:170px; border-top:none; height:100%;}
.right_up{ border-top:1px solid #c0d2da;}

.query_bar,.nav_bar{ height:30px; float:left; width:820px; line-height:24px;}
.nav_bar{ color:#0063a1; background-color:#ececec; height:28px; margin-bottom:8px; line-height:26px; padding-left:8px;}
.nav_bar a{ color:#0063a1;}
.nav_bar a:hover{ color:#ff0000;}
.nav_bar2{ overflow:hidden;}
.query_tit{ height:16px; width:140px;}
.query_btn{ width:48px; height:21px; border:none; background:url(../images/btnbj.gif) no-repeat 0; cursor:pointer;}
.list_tit{ float:left; width:820px;}
.list_bt{ background:#ececec; border-bottom:1px solid #e0e0e0; border-top:1px solid #fff; height:23px; font-weight:bold; line-height:24px;}
.list_biaoti{ padding-left:90px; float:left;}
.list_timebt{ float:right; padding-right:30px;}

.hd_bt{color: #000000;
    font-family: "宋体",arial;
    font-size: 26px;
    font-size-adjust: none;
    font-stretch: normal;
    font-style: normal;
    font-variant: normal;
    font-weight: 800;
    line-height: 30px;
    padding:20px 0 8px 0;
	text-align:center;}
.tit_Bar{ text-align:center; display:block; color:#666;}
.content_dis{ padding:15px; font-size:14px; line-height:28px;}
.quick2_set{ background:url(../images/btn_03.gif) no-repeat 0 0;}
.window_set{ background:url(../images/btn_05.gif) no-repeat 0 0;}
.quick2_set,.window_set{ width:107px; height:30px; text-align:center; display:inline-block; line-height:30px;}
.window_set:hover,.quick2_set:hover{ font-weight:bold; text-decoration:none;}

.add{ background:url(../images/add.png) no-repeat left center; padding-left:20px; display:inline-block;}
.delete{ background:url(../images/cross.png) no-repeat left center; padding-left:20px; display:inline-block;}
.add2{ background:url(../images/add2.png) no-repeat left center; padding-left:20px; display:inline-block; color:#999;}
.delete2{ background:url(../images/cross2.png) no-repeat left center; padding-left:20px; display:inline-block; color:#999;}

.setting-function{ float:right; margin-right:5px; width:100px;}
.set_Tit{ float:left; display:inline; padding-left:10px;}

.ccid-footer{ border-top:1px solid #BBCFD8; margin-top:15px; margin-bottom:20px; padding-top:10px; padding-bottom:10px; overflow:hidden; text-align:center;}





.zidaoh{ background:url(../images/zidaoh.gif) no-repeat left center; height:25px; padding-left:18px; line-height:24px;}
.cxbg_text{ width:120px; height:21px; border-left: solid 1px #bebebe; border-top:solid 1px #bebebe; border-bottom:solid 1px #e1e1e1; border-right:solid 1px #e1e1e1; background:#fff; vertical-align:middle;}
.cxbg_text1{ width:80px; height:21px; border-left: solid 1px #bebebe; border-top:solid 1px #bebebe; border-bottom:solid 1px #e1e1e1; border-right:solid 1px #e1e1e1; background:#fff; vertical-align:middle;}
.cxbg_text2{ width:80px; height:21px; border-left: solid 1px #bebebe; border-top:solid 1px #bebebe; border-bottom:solid 1px #e1e1e1; border-right:solid 1px #e1e1e1; background:#fff; vertical-align:middle;line-height:21px;text-align:center;}
.cxbg_text3{ width:120px; height:21px; border-left: solid 1px #bebebe; border-top:solid 1px #bebebe; border-bottom:solid 1px #e1e1e1; border-right:solid 1px #e1e1e1; background:#fff;text-align:left;vertical-align:middle;line-height:21px;text-align:center;}
.chaxunbtn{ background:url(../images/cxx_03.png) no-repeat left center; width:64px; height:25px; border:none; display:inline-block; vertical-align:middle; cursor:pointer;}
.biaokua{ border:solid 1px #d4d4d4; width:100%;}
#biaohead{ padding:1px; background:url(../images/biaojian_07.gif) repeat-x center center; height:32px; text-align:left;}
#biaohead img{ vertical-align:-3px;}
.biaokua2{border-right:solid 1px #d4d4d4; width:100%;}
.biaokua2 td{ height:33px; text-align:center;}
.biaokua2 th{ height:33px; text-align:center;}
.trodd{ background:url(../images/lbx_11.png) repeat-x center center;}
.yeshu{ width:330px; float:right;}
.yeshu span a{ border:1px solid #a3a3a3; padding:1px 5px;}
.yeshu span .select{ border:1px solid #245b8e; background-color:#417eb7; color:#fff; text-decoration:none;}
.yeshu span a:hover{ border:1px solid #245b8e; background-color:#417eb7; color:#fff; text-decoration:none;}
.biaokua3{ border:solid 1px #d4d4d4; width:100%;}
.biaokua3{border-right:solid 1px #d4d4d4; width:100%;}
.biaokua3 td{ height:53px; }
.biaokua3 th{ height:53px; }

/* #indexMenu{
	margin-top:15px;
}
 */

