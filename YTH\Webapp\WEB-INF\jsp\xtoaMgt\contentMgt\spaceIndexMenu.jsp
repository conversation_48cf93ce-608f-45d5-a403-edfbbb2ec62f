<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
	System.out.println(path);
%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<base href="<%=basePath%>">

<title>spaceIndexMenu</title>

<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
<meta http-equiv="description" content="This is my page">
<!--
	<link rel="stylesheet" type="text/css" href="styles.css">
	-->
<script type="text/javascript"
	src="${pageContext.request.contextPath }/js/jquery/jquery-1.7.2.js"></script>
<script
	src="<%=request.getContextPath()%>/background/codebase/dhtmlxcommon.js"></script>
<script
	src="<%=request.getContextPath()%>/background/codebase/dhtmlxtreeindex.js"></script>
<script
	src="<%=request.getContextPath()%>/background/codebase/dhtmlxmenu.js"></script>
<style type="text/css">
.menu {
	margin-bottom: 1px;
	width: 100%;
}

.menu_title {
	background: url("${pageContext.request.contextPath}/images/dd_15.jpg")
		repeat-x 0px center;
	height: 31px;
	line-height: 31px;
	vertical-align: middle;
	text-align: center;
	margin-bottom: 1px;
	color: #FFFFFF;
	font-weight: bold;
	font-size: 0.75em;
	width: 100%;
}
.menu_title li{
	list-style-type:none;
}

.menu_items li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	/* font-size: 0.75em;
	font-family: "宋体"; */
	color: #2F2F2F;
	width: 85%;
	font-family: "Microsoft YaHei", Arial, sans-serif;
	letter-spacing: 0.2em; /* 根据需要调整间距大小 */
	font-size: 12px;
}
.menu_items span{
	font-size: 1em;
}

.treeindex {
list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	line-height: 30px;
	font-size: 12px;
	font-family: "宋体";
	color: #2F2F2F;
	width: 85%;
	text-indent:0.9em;
}


#impmeeting li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	font-family: "宋体";
	color: #2F2F2F;
	width: 85%;
	text-indent:2em;
}

#gwtd1 li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	font-family: "宋体";
	color: #2F2F2F;
	width: 85%;
	text-indent:4em;
}

#gwbl1 li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	font-family: "宋体";
	color: #2F2F2F;
	width: 85%;
	text-indent:2em;
}
#touqia1 li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	/* font-family: "宋体"; */
	color: #2F2F2F;
	width: 85%;
	text-indent:2em;
	font-family: "Microsoft YaHei", Arial, sans-serif;
	letter-spacing: 0.2em; /* 根据需要调整间距大小 */
}
#touqia2 li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	font-family: "宋体";
	color: #2F2F2F;
	width: 85%;
	text-indent:4em;
}
#touqia0 li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	font-family: "宋体";
	color: #2F2F2F;
	width: 85%;
	text-indent:4em;
}
#event1 li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	/* font-family: "宋体"; */
	color: #2F2F2F;
	width: 85%;
	text-indent:2em;
	font-family: "Microsoft YaHei", Arial, sans-serif;
	letter-spacing: 0.2em; /* 根据需要调整间距大小 */
}

#modular li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	font-family: "宋体";
	color: #2F2F2F;
	width: 85%;
	text-indent:2em;
}

#xuanc li {
	list-style: none;
	background: url("${pageContext.request.contextPath}/images/dd.png")
		no-repeat 20px center;
	text-align: left;
	padding-left: 15%;
	cursor: pointer;
	height: 30px;
	line-height: 30px;
	font-size: 12px;
	font-family: "宋体";
	color: #2F2F2F;
	width: 85%;
	text-indent:2em;
}


.menu_items ul {
	margin: 0px;
	padding: 0px;
	width: 100%;
}
..containerTableStyle
</style>
<script type="text/javascript">
 	
 	$(function(){
		$.ajax({
			type:"POST",
			url: "/workassignmentMgt_timerRwTask.action",
			dataType:"json",
			success: function (data) {
				if(data.msg == "true"){
					//var imgSrc = document.getElementById("imgSrc");
					$("#imgSrc").html("[<span>新工作</span>]"); 
				}else{
					//var imgSrc = document.getElementById("imgSrc");
					$("#imgSrc").html(""); 
				}
			}
		});
	});  
	
/* 	function timerRwTask(){
		$.ajax({
			type:"POST",
			url: "/workassignmentMgt_timerRwTask.action",
			dataType:"json",
			success: function (data) {
				if(data.msg == "true"){
					//var imgSrc = document.getElementById("imgSrc");
					$("#imgSrc").css("display","block");
				}else{
					//var imgSrc = document.getElementById("imgSrc");
					$("#imgSrc").css("display","none");
				}
			}
		});
	}
	window.setInterval(timerRwTask, 1000); */
	
	
	
/* 	function daibanalert(){
		$.ajax({
			type:"POST",
			url: "/contentMgt_daibantixing.action",
			dataType:"json",
			success: function (data) {
				
				if(data.msg == "true"){
					//alert("1");
					//var daiban = document.getElementById("approve");
					$("#daiban").html("[<span>新工作</span>]"); 
					$("#approve").html("[<span>新工作</span>]"); 
					//$("#imgSrc").css("display","block");
				}else{
					//alert("2");
					$("#daiban").html(""); 
					$("#approve").html(""); 
					//var imgSrc = document.getElementById("imgSrc");
					//$("#imgSrc").css("display","none");
				}
			}
		});
	}
	window.setInterval(daibanalert, 600000); */
	
	/*
 	$(function(){
 		$.ajax({
			type:"POST",
			url: "/contentMgt_daibantixing.action",
			dataType:"json",
			success: function (data) {
				
				if(data.msg == "true"){
					//alert("1");
					//var daiban = document.getElementById("approve");
					$("#daiban").html("[<span>新工作</span>]"); 
					$("#approve").html("[<span>新工作</span>]"); 
					//$("#imgSrc").css("display","block");
				}else{
					//alert("2");
					$("#daiban").html(""); 
					$("#approve").html(""); 
					//var imgSrc = document.getElementById("imgSrc");
					//$("#imgSrc").css("display","none");
				}
			}
		});
	}); */
	
	
/* 	function tongzhialert(){
		$.ajax({
			type:"POST",
			url: "/MessageAction_tongzhitixing.action",
			dataType:"json",
			success: function (data) {
				
				if(data.msg == "true"){
					//alert("1");
					//var daiban = document.getElementById("approve");
					$("#count").html("[<span>新通知</span>]"); 
					//$("#approve").html("[<span>新工作</span>]"); 
					//$("#imgSrc").css("display","block");
				}else{
					//alert("2");
					$("#count").html(""); 
					//$("#approve").html(""); 
					//var imgSrc = document.getElementById("imgSrc");
					//$("#imgSrc").css("display","none");
				}
			}
		});
	}
	window.setInterval(tongzhialert, 600000); */
 	$(function(){
 		$.ajax({
			type:"POST",
			url: "/MessageAction_tongzhitixing.action",
			dataType:"json",
			success: function (data) {
				
				if(data.msg == "true"){
					//alert("1");
					//var daiban = document.getElementById("approve");
					$("#count").html("[<span>新通知</span>]"); 
					//$("#approve").html("[<span>新工作</span>]"); 
					//$("#imgSrc").css("display","block");
				}else{
					//alert("2");
					$("#count").html(""); 
					//$("#approve").html(""); 
					//var imgSrc = document.getElementById("imgSrc");
					//$("#imgSrc").css("display","none");
				}
			}
		});
	}); 
 	$(function(){
 		$.ajax({
			type:"POST",
			url: "/updownAction_tongzhitixing.action",
			dataType:"json",
			success: function (data) {
				
				if(data.msg == "true"){
					//alert("1");
					//var daiban = document.getElementById("approve");
					$("#tongbao").html("[<span>新</span>]"); 
					//$("#approve").html("[<span>新工作</span>]"); 
					//$("#imgSrc").css("display","block");
				}else{
					//alert("2");
					$("#tongbao").html(""); 
					//$("#approve").html(""); 
					//var imgSrc = document.getElementById("imgSrc");
					//$("#imgSrc").css("display","none");
				}
			}
		});
	});
	$(function() {
		$(".menu_items > ul > li").mouseover(function() {
			$(this).css("background-color", "#AAAAAA");
		}).mouseout(function() {
			$(this).css("background-color", "#FFFFFF");
		});
		$("#impmeeting  > li").mouseover(function() {
			$(this).css("background-color", "#AAAAAA");
		}).mouseout(function() {
			$(this).css("background-color", "#FFFFFF");
		});
		$("#event1  > li").mouseover(function() {
			$(this).css("background-color", "#AAAAAA");
		}).mouseout(function() {
			$(this).css("background-color", "#FFFFFF");
		});
		
		$("#xuanc  > li").mouseover(function() {
			$(this).css("background-color", "#AAAAAA");
		}).mouseout(function() {
			$(this).css("background-color", "#FFFFFF");
		});
		
		
		
		$("#gwtd1  > li").mouseover(function() {
			$(this).css("background-color", "#AAAAAA");
		}).mouseout(function() {
			$(this).css("background-color", "#FFFFFF");
		});
		$("#gwbl1  > li").mouseover(function() {
			$(this).css("background-color", "#AAAAAA");
		}).mouseout(function() {
			$(this).css("background-color", "#FFFFFF");
		});
		$("#touqia1  > li").mouseover(function() {
			$(this).css("background-color", "#AAAAAA");
		}).mouseout(function() {
			$(this).css("background-color", "#FFFFFF");
		});
		$(".containerTableStyle > table > tr").mouseover(function() {
			$(this).css("background-color", "#AAAAAA");
		}).mouseout(function() {
			$(this).css("background-color", "#FFFFFF");
		});
		//$("#calendar").click(function(){
		//	window.parent.document.getElementById("mainframe").src="/calendar.html";
		//});
		$("#event")
				.click(
						function() {
							var url = "/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1";
							//var url = "/workflowEngine/background/templeMgtAction_processingWFList.action?bizType=-1";
							window.parent.document.getElementById("mainframe").src = url;
						});
		//非流程提醒
		$("#toRemind")
				.click(
						function() {
							var url = "/workflowEngine/background/templeMgtAction_remindList.action";
							//var url = "/workflowEngine/background/templeMgtAction_processingWFList.action?bizType=-1";
							window.parent.document.getElementById("mainframe").src = url;
						});
		
		$("#daiban")
					.click(
						function() {
							var url = "/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1";
							//var url = "/workflowEngine/background/templeMgtAction_processingWFList.action?bizType=-1";
							window.parent.document.getElementById("mainframe").src = url;
						});
		
		$("#email").click(function() {
			var url = "http://mail.ccidit.com";
			window.parent.document.getElementById("mainframe").src = url;
		});
		$("#message").click(function() {
			var url = "/officeMgt_myMessage.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		//	$("#content").click(function(){
		//		var url = "/contentMgt_listAllArticles.action?funcCode=27-1-6-&tempStr=1350621791763";
		//		window.parent.document.getElementById("mainframe").src=url;
		//	});
		$("#contact")
				.click(
						function() {
							var url = "/officeMgt_addressList.action?a=1&funcCode=27-1-1-&tempStr=1350627156722";
							window.parent.document.getElementById("mainframe").src = url;
						});
		$("#password").click(function() {
			var url = "/userMgt_passwordPage.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		$("#question").click(function() {
			var url = "/userMgt_test.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		$("#selfpage").click(function() {
			var url = "/userMgt_selfpage.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		$("#feedback")
				.click(
						function() {
							var url = "/workflowEngine/background/templeMgtAction_myWFList.action?bizType=1118";
							window.parent.document.getElementById("mainframe").src = url;
						});
		$("#contactus").click(function() {
			var url = "/userMgt_contactus.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		$("#downloads01").click(function() {
			var url = "/downloads/院一体化-使用手册.doc";
			window.parent.document.getElementById("mainframe").src = url;
		});
		$("#addres").click(function() {
			var url = "/userMgt_gotoAddressBook.action";
			window.parent.document.getElementById("mainframe").src = url;
		});

		$("#toRead")
				.click(
						function() {
							var url = "/workflowEngine/background/templeMgtAction_readList.action?bizString=-1";
							window.parent.document.getElementById("mainframe").src = url;
						});
		$("#toweekly")
				.click(
						function() {
							var url = "/weeklySummaryAction_listWeeklySummary.action?querytype=例会重要事项";
							window.parent.document.getElementById("mainframe").src = url;
						});

		$("#myAttention")
				.click(
						function() {
							var url = "/workflowEngine/background/templeMgtAction_myAttentionList.action?bizString=-1";
							window.parent.document.getElementById("mainframe").src = url;
						});
		
		//在线聊天
		$("#toGroupchat")
		.click(function(){
			var url = "/MessageAction_toGroupchat.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		
		//资讯库
		$("#toProduct")
		.click(function(){
			var url = "/MessageAction_toProduct.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		
		//岗位工作
		$("#toPostwork")
		.click(function(){
			var url = "/MessageAction_toPostwork.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		//制度库
		$("#toGWzhidu")
		.click(function(){
			var url = "/MessageAction_toGWzhidu.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		
		
		//工作分配
		$("#toWorkassignment")
		.click(function(){
			var url = "/MessageAction_toWorkassignment.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		
		//目录录入
		$("#addReg")
		.click(function(){
			var url = "/compilationMgt_addReg.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		
		//规定汇编
		$("#toCatalogEntry")
		.click(function(){
			var url = "/MessageAction_toCatalogEntry.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		
		//外币录入
		$("#toCurrency")
		.click(
				function() {
					var url = "/MessageAction_toCurrency.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		//跨境产业平台
		$("#toPlatform")
		.click(
				function() {
					var url = "/MessageAction_toPlatform.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		//通知公告
		$("#toMessage")
		.click(
				function() {
					var url = "/MessageAction_messageList.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		//日程安排
		$("#toMeeting")
		.click(
				function() {
					var YEAR = $("#TYEAR").val();  
					var MONTH = $("#TMONTH").val();
					var DAY = $("#TDAY").val();
					//var url = "/meetingAction_list.action?ownerType=department";
					var url = "/meetingAction_Alllist.action?ownerType=department"+'&YEAR='
						+ YEAR+'&MONTH='+MONTH+'&DAY='+DAY;
					window.parent.document.getElementById("mainframe").src = url;
				});
		//工作部署
		$("#towork")
		.click(
				function() {
					var url = "/updownAction_list.action?fl=GZBS";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//重要通知
		$("#tonotice")
		.click(
				function() {
					var url = "/updownAction_list.action?fl=ZYTZ";
					window.parent.document.getElementById("mainframe").src = url;
				});		
		//专项议题
		$("#todiscuss")
		.click(
				function() {
					var url = "/updownAction_list.action?fl=ZXYT";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//经济形势通报
		$("#tojingji")
		.click(
				function() {
					var url = "/updownAction_list.action?fl=JJXSTB";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		$("#ghtw")
		.click(
				function() {
					var url = "/updownAction_list.action?fl=GHTW";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//计划
		$("#toWeekly")
		.click(
				function() {
					var url = "/weeklySummaryAction_listWeeklySummary.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		//公文课堂
		$("#td0")
		.click(
				function() {
					var url = "/MessageAction_messageList2.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		
		
		
		$("#td1")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=tyz/swb/swb_qsbg_list.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#td2")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=tyz/swb/swb_fwgz_list.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#td3")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=tyz/swb/swb_dbbl_list.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#td4")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=tyz/swb/swb_wdcl_list.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#td5")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=tyz/swb/swb_jgfd_list.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#td6")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=tyz/swb/swb_wjgfd_list.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#td7")
		.click(
				function() {
					var url = "/frameworkMgt_feishemihanjian.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#td8")
		.click(
				function() {
					var url = "/frameworkMgt_dangweiwenjian.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#td9")
		.click(
				function() {
					//var url = "/frameworkMgt_baitouwenjian.action";
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=tyz/swb/processing_sheet_list.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#td10")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=tyz/swb/wanderAbout_list.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
//************************************
		$("#tdcost2")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=cost2/cost2.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
//************************************				
		$("#muban")
		.click(
				function() {
					var url = "/updownAction_list.action?fl=BGCYMBXZ";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#tdcost")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=cost/cost.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		
		$("#xuanchuan")
		.click(
				function() {
					var url = "/frameworkMgt_pro.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#public")
		.click(
				function() {
					//var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=public/public.cpt";
					var url="/meetingAction_publiclist.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#export")
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=pro/selectpro.cpt&op=write";
					//var type="&op=write";
					window.parent.document.getElementById("mainframe").src = url;
					//window.location.href="/tyMgtServer?url=fineServer/ReportServer?reportlet=pro/selectpro.cpt&op=write";
				});
		$("#juphonelist")
		.click(
				function() {
					//var url = "/tyMgtServer?url=fineServer/ReportServer?reportlet=pro/selectpro.cpt";
					//var type="&op=write";
					window.parent.document.getElementById("mainframe").src = "/tyMgtServer?url=fineServer/ReportServer?reportlet=phone.cpt&op=write";
					//window.location.href="/tyMgtServer?url=fineServer/ReportServer?reportlet=pro/selectpro.cpt&op=write";
					//window.parent.document.getElementById("mainframe").src ="http://biz.cipa.org.cn/phone.jpg";
					//window.open("http://biz.cipa.org.cn/phone.jpg", "_blank");
				});
		
		
		$("#basic")//基本情况
		.click(
				function() {
					var url = "/updownAction_list.action?fl=basic";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#politics")//政治建设情况
		.click(
				function() {
					var url = "/updownAction_list.action?fl=politics";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#thought")//思想建设情况
		.click(
				function() {
					var url = "/updownAction_list.action?fl=thought";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#organization")//组织建设情况
		.click(
				function() {
					var url = "/updownAction_list.action?fl=organization";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#style")//作风建设情况
		.click(
				function() {
					var url = "/updownAction_list.action?fl=style";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#discipline")//纪律建设情况
		.click(
				function() {
					var url = "/updownAction_list.action?fl=discipline";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#system")//制度建设情况
		.click(
				function() {
					var url = "/updownAction_list.action?fl=system";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#response")//平台信息动态响应记录表
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=response/responselist.cpt&op=write";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#achievementFile")//成果文件预览
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=response/achievementFile.cpt&op=write";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#internalProcurementQD")//内部采购机制公开表
		.click(
				function(){
			var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=response/internalProcurementQD.cpt&op=write";
			window.parent.document.getElementById("mainframe").src = url;
		});
		$("#internalProcurementCY")//内部采购机制公开表
		.click(
				function(){
			var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=response/internalProcurementCY.cpt&op=write";
			window.parent.document.getElementById("mainframe").src = url;
		});
		
		$("#workcount")//工作统计表表
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=work/workcount.cpt&op=write";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#filecount")//工作附件统计表表
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=work/filecount.cpt&op=write";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#yiban")//已办
		.click(
				function() {
					var url = "/workflowEngine/background/templeMgtAction_processedWFList.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#signitem")//已办
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=sign/signlist.cpt&op=write";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#hetong")//已办
		.click(
				function() {
					
					
			    	var path = "<%=request.getContextPath()%>";
			    	
					var url = path + "/attachAction_downloadZipHt.action";
					//window.location.href = url;
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#jurole")//规章制度
		.click(
				function() {
					var url = "/contentMgt_listjurole.action?type=0";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#gongzhang")//公章申请
		.click(
				function() {
					var url = "/workflowEngine/background/templeMgtAction_myWFList.action?bizType=20210311";
					window.parent.document.getElementById("mainframe").src = url;
				});
		
		$("#lizhi")//离职申请
		.click(
				function() {
					var url = "/workflowEngine/background/templeMgtAction_myWFList.action?bizType=20210401";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#gongzhangfinally")//公章申请
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=queryOfficialSeal.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#yusuan")//预算统计
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=yusuancount.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#HTcount")//合同一览
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=HTcount.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#updateLog")//更新 日志
		.click(
				function() {
					var url = "/contentMgt_listupdateLog.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#allxietong")//岗位管理
		.click(
				function() {
					var url = "/MessageAction_allxietong.action";
					window.parent.document.getElementById("mainframe").src = url;
				});
		//全局协同工作库管理
		$("#toGuanli")
		.click(function(){
			var url = "/MessageAction_toGuanli.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		
		//全局协同工作库查询
		$("#toChaxun")
		.click(function(){
			var url = "/MessageAction_toChaxun.action";
			window.parent.document.getElementById("mainframe").src = url;
		});
		$("#dutyoff")//日程安排休假一览表
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=dutyoff.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#xiaojia")//销假
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=modifyproject/xiaojiaguanli.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		$("#cc_seach")//合同一览
		.click(
				function() {
					var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=cc_seach.cpt";
					window.parent.document.getElementById("mainframe").src = url;
				});
		//投洽专栏-综合协调-筹备进展
		$("#TQ1-1")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=1-1";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-综合协调-对外宣介材料
		$("#TQ1-2")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=1-2";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-综合协调-活动一览表
		$("#TQ1-3")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=1-3";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-综合协调-重要嘉宾
		$("#TQ1-4")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=1-4";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-综合协调-往届材料
		$("#TQ1-5")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=1-5";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		
		//投洽专栏-活动筹备-国际组织合作
		$("#TQ2-1")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=2-1";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-活动筹备-四大IP招商板块
		$("#TQ2-2")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=2-2";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-活动筹备-部办活动
		$("#TQ2-3")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=2-3";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-活动筹备-其他活动
		$("#TQ2-4")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=2-4";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-新闻宣传
		$("#TQ3")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=3";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-信息及项目
		$("#TQ4")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=4";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-会务保障
		$("#TQ5")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=5";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-党建纪检
		$("#TQ6")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=6";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-评估
		$("#TQ7")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?fl=7";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		
		
		//投洽专栏-党办
		$("#dangban")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=465";
					window.parent.document.getElementById("mainframe").src = url;
				});	

		//投洽专栏-风险
		$("#fengguan")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=440";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-综合部
		$("#zonghe")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=436";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-信息部
		$("#xinxi")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=437";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-国内联络部
		$("#guonei")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=441";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-国际联络部
		$("#guoji")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=464";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-会展部
		$("#huizhan")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=438";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-对外投资合作部
		$("#duiwai")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=439";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-产业一部
		$("#chanyi")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=430";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-产业二部
		$("#chaner")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=431";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-产业三部
		$("#chansan")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=432";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-产业四部
		$("#chansi")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=433";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-产业五部
		$("#chanwu")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=434";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-产业六部
		$("#chanliu")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=469";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		//投洽专栏-投洽办
		$("#touqiaban")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=448";
					window.parent.document.getElementById("mainframe").src = url;
				});
		//投洽专栏-中心专班
		$("#zhuanban")
		.click(
				function() {
					var url = "/updownAction_listtouqia.action?dept_id=470";
					window.parent.document.getElementById("mainframe").src = url;
				});	
		/* $("#sxsh")
		.click(
				function() {
					var url = "http://biz.cipa.org.cn/workflowEngine/background/templeMgtAction_homeList.action?bizString=9001,9002,9003,5001,6016,6012,6015&funcCode=31-&funcCode=31-&module=";
					 window.location.href = url;
				});
		$("#cwsx")
		.click(
				function() {
					var url = "http://biz.cipa.org.cn/workflowEngine/background/templeMgtAction_homeList.action?bizString=8001,8002,8003,8006&funcCode=36-&funcCode=36-&module=cwfzMgt";
					window.open(url);
				}); */
		
		
		
		
		
/*  		var url="/workflowEngine/background/templeMgtAction_notReadCount.action";
		$.ajax({ //调用jquery的ajax方法    
				type : "POST", //设置ajax方法提交数据的形式    
				url : url, //把数据提交到deptMgt_addDept.action
				
				success : function(msg) {
					var count = msg;
					if(count!=null&&count!=0 && count!='0'){
						var str='<font style="color:red; font-weight: bolder; font-size: 14px; ">['+count+']</font>';
						$("#count").html(str);
					}
				}
			});  */
<%--
	var url = "/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1";
		//var url = "/workflowEngine/background/templeMgtAction_processingWFList.action?bizType=-1";
		window.parent.document.getElementById("mainframe").src = url;
		--%>
	});

	function setReadCount() {
		var url = "/workflowEngine/background/templeMgtAction_notReadCount.action";
		$
				.ajax({ //调用jquery的ajax方法    
					type : "POST", //设置ajax方法提交数据的形式    
					url : url, //把数据提交到deptMgt_addDept.action

					success : function(msg) {
						var count = msg;
						if (count != null) {
							if (count == 0 || count == '0') {
								$("#count").html("");
							} else {
								var str = '<font style="color:red; font-weight: bolder; font-size: 14px; ">['
										+ count + ']</font>';
								$("#count").html(str);
							}

						}

					}
				});
	}

	function isHidden(obj){
      var vDiv = document.getElementById(obj.id);
      vDiv.style.display = (vDiv.style.display == 'none')?'block':'none';
    }
	function divhidden3(){
		 
/* 		var vDiv1 = document.getElementById("menu_items1");
		var vDiv2 = document.getElementById("menu_items2");
		var vDiv3 = document.getElementById("menu_items3");
		vDiv1.style.display='none';
		vDiv2.style.display='none';
		vDiv3.style.display='block'; */
		//alert("暂未开通！");
		var url = "tyMgtServer?url=fineServer/ReportServer?reportlet=cost/cost.cpt";
		window.parent.document.getElementById("mainframe").src = url;
	}
	//树形结构初始化-----------------------------------------------------////
	$(document)
			.ready(
					function() {
						//var funcCode = $('#funcCode').val();
						var funcCode='31-';
						//alert(funcCode);
						//var funcUrl = $('#funcUrl').val();
						var funcUrl="http://biz.cipa.org.cn/workflowEngine/background/templeMgtAction_homeList.action?bizString=9001,9002,9003,5001,6016,6012,6015&funcCode=31-&funcCode=31-&module=";
						//var funcUrl ="/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=43-&funcCode=43-&module=";
						//alert(funcUrl);
						
						tree = new dhtmlXTreeObject("divTree1", "100%",
								"100%", -1);
						//link tree to asp script
						tree.setImagePath("${ssourl}/background/codebase/imgs/edit/");
						tree.setSkin("dhx_skyblue");					
						tree.setXMLAutoLoading("frameworkMgt_getRootMenuXMLindex.action");
						//load first level of tree
						tree.setOnClickHandler(displayPic);//设置鼠标左键单击事件
						tree.setOnOpenHandler(displayPic);
						 tree.setOnRightClickHandler(openOtherUrl);
						tree.attachEvent("onClick", requestUrl);
						tree.enableHighlighting(true);
						tree.attachEvent("onXLE", function(tree, id) {
							removewait();
							var subTreeIds = tree.getAllSubItems(id);
							var idArr = subTreeIds.split(",");
							for ( var i = 0; i < idArr.length; i++) {
								var iscolor = tree.getUserData(idArr[i],
										"iscolor");
								if (iscolor == 1) {
									tree.setItemColor(idArr[i], "#aaa",
											"#aaa");
								}

							}
						});

						/* var indexUrl = '<s:url action="contentMgt_personalSpaceIndex" namespace="/" />';
						openIndex(indexUrl); */

						$("#link").attr("title", "keleyi.com");
						tree.setOnMouseOutHandler(ouseOutHandlerFunc);
						if (funcCode!= "" && funcUrl != "" && funcUrl != "undefined"){
							openSelf(funcUrl, funcCode);
						}else{
							tree._loadDynXML("frameworkMgt_getRootMenuXMLindex.action?id="+ funcCode);
						}
					});
	function ouseOutHandlerFunc(id) {
		$(".standartTreeRow").each(function() {
			$(this).attr("title", $(this).text());
		});

	}

	function displayPic(id) {
		var state = tree.getOpenState(id);//获得当前点击合拢状态
		if (state <= 0) {
			//beginwait("alldivTreeArea",'正在加载菜单');
			tree.deleteChildItems(id);
			tree.setXMLAutoLoading("frameworkMgt_getSubMenuXMLindex.action");
			tree._loadDynXML("frameworkMgt_getSubMenuXMLindex.action?id=" + id);
		} else {
			tree.closeAllItems(id);
		}
	}

	function requestUrl(id) {
		if (tree.getUserData(id, "url") != null) {
			var myDate = new Date();
			var mytime = myDate.getTime();
			var url = tree.getUserData(id, "url");
			url += "&tempStr=" + mytime;
			$(window.parent.document.getElementById("mainframe").src = url);
		}
		return true;
	}
	function openOtherUrl(id) {
		if (tree.getUserData(id, "url") != null) {
			var myDate = new Date();
			var mytime = myDate.getTime();
			var url = tree.getUserData(id, "url");
			url += "&tempStr=" + mytime;
			window.open(url);
		}
	}
	function openSelf(url, funcCode) {
		//beginwait("alldivTreeArea",'正在加载菜单');
		tree.deleteChildItems("-1");
		tree.setXMLAutoLoading("frameworkMgt_getRootMenuXMLindex.action");
		tree._loadDynXML("frameworkMgt_getRootMenuXMLindex.action?id="+ funcCode);
			//tree.openAllItems("-1");
			var subTreeIds = tree.getAllSubItems("-1");
			var idArr = subTreeIds.split(",");
			for ( var i = 0; i < idArr.length; i++) {
				tree.openAllItems(idArr[i]);
			}
		$("#divTree1 > .containerTableStyle").show();
		$("#divTree1 > #indexMenu").remove();
		//$(window.parent.document.getElementById("mainframe").src = url);
		// $(window.parent.document.getElementById("mainframe")).reload();
		//document.getElementById("mainFrame").src=url;
	}

	function openIndex(indexUrl) {
		var menuUrl = '<s:url action="contentMgt_spaceIndexMenu" namespace="/" />';
		var wid = $("#divTree1").width();
		//           	"<iframe src='"+menuUrl+"' width='"+wid+"' height='100%' scrolling='no' frameborder='0' />"
		$("#divTree1 > .containerTableStyle").hide();
		$("#divTree1 > #indexMenu").remove();
		$("#divTree1").append("<iframe id='indexMenu' src='"
								+ menuUrl
								+ "' width='"
								+ wid
								+ "' height='100%' scrolling='auto' frameborder='0' />");
		window.parent.document.getElementById("mainframe").src = indexUrl;
	}

	function openSpaceIndex(indexUrl, idIndex) {

		for ( var i = 1; i <= 4; i++) {
			if (i == idIndex) {
				$("#spaceIndex_" + i).css({
					color : "#E90000"
				});
			} else {
				$("#spaceIndex_" + i).css({
					color : "#2F2F2F"
				});
			}
		}
		var menuUrl = '<s:url action="contentMgt_spaceIndexMenu" namespace="/" />';
		var wid = $("#divTree1").width();

		$("#divTree1 > .containerTableStyle").hide();
		$("#divTree1 > #indexMenu").remove();
		$("#divTree1").append("<iframe id='indexMenu' src='"
								+ menuUrl
								+ "' width='"
								+ wid
								+ "' height='100%' scrolling='no' frameborder='0' />");
		window.parent.document.getElementById("mainframe").src = indexUrl;
	}

	function showMoreMessage() {
		var url = "/officeMgt_myMessage.action";
		window.parent.document.getElementById("mainframe").src = url;
	}
	
	$(function(){
		$.ajax({
			   type:"POST",
			   url: "/postworkMgt_selectUserVoAjax.action",
			   dataType:"json",
			   success: function (data) {
				   $.ajax({
						type:"GET",
						url: "http://***********:8686/product/getRemindDept?userId="+data.id,
						dataType:"json",
						success: function (res) {
							if(res.data == 0){
								$("#realCount").html("[<span>新资讯</span>]"); 
							}
						}
					});
			   }
		});
	}); 
</script>
</head>

<body style="margin:0px;padding:0px;" onload="isHidden(event1)">
		<input type="hidden" class="cxbg_text" id="TYEAR" value="${YEAR}" />
		<input type="hidden" class="cxbg_text" id="TMONTH" value="${MONTH}" />
		<input type="hidden" class="cxbg_text" id="TDAY" value="${DAY}" />
	 <!-- 
	 <c:if test="${user.deptId == 437 }">
	  
	<div class="menu"> 
		<div class="menu_title"  >在线聊天</div>
		<div class="menu_items" style="" id="menu_items2">
			<ul>
				  <li id="toGroupchat">在线聊天</li>
			</ul>
		</div>
	</div>
	 </c:if>
	   -->
	   <c:if test="${user.id == 1462 }">
	    	<div class="menu"> 
		<div class="menu_title"  >常用功能</div>
		<div class="menu_items" style="" id="menu_items2">
 			<ul>  
				  <li id="toMessage">通知公告<span id="count" style="color:red;"></span></li>
				<li id="toMeeting">日程安排</li>
			</ul> 
		</div>
	</div> 
	   </c:if>
	   <c:if test="${user.id != 1462 }">
	   <div class="menu"> 
			<div class="menu_title"  >资讯库</div>
			<div class="menu_items" style="" id="menu_items2">
	 			<ul>  
					  <li id="toProduct">资讯库<span id="realCount"  style="color:red"></span></li>
				</ul> 
			</div>
		</div> 
	   
<%-- 	   <c:if test="${ user.id!=1430   and user.id!=1427}"> <!-- 国际联络部 郁泽和方敏敏；会展部常晟和孙贤铭-->
 	<div class="menu"> 
		<div class="menu_title"  >工作交互</div>
		<div class="menu_items" style="" id="menu_items2">
 			<ul>  
				  <li id="toWorkassignment">工作交互<span id="imgSrc"  style="color:red"></span></li>
			</ul> 
		</div>
	</div> 	
	</c:if> --%>
	<c:if test="${ user.id!=1430   and user.id!=1427}"> <!-- 国际联络部 郁泽和方敏敏；会展部常晟和孙贤铭-->
	<div class="menu"> 
		<div class="menu_title"  >公共板块</div>
		<div class="menu_items" style="" id="menu_items2">
			<ul>
			<c:if test="${user.id != 1486 and user.id != 1205 and  user.id != 1517  and user.id != 1457 and user.id != 1476}">
				  <li id="toPostwork">我的岗位</li>
				</c:if>
				   <li id="toGWzhidu">制度库</li>
				 
				   <c:if test="${user.id == 135||user.id==1385 }"><!-- admin和admin1显示-->
				      <li id="toGuanli">全局协同工作库管理</li> 
				      </c:if>
				       <!--  <li id="toChaxun">全局协同工作库</li>  -->
				<c:if test="${user.id == 135||user.id==1385 }"><!-- admin和admin1显示-->
				<li id="workcount">工作统计</li>
				<li id="filecount">附件统计</li>
				</c:if>	
				<!-- <li id="xuanchuan"  onclick="isHidden(xuanc)">宣传素材</li> -->
				<li id="tonotice">宣传素材</li>
				<Li id="public">公共资源</Li>
				<li id="tojingji">经济形势通报<span id="tongbao" style="color:red;"></span></li>
				<c:if test="${user.id != 1486 }">
				<li id="muban">常用模板</li>
				<li id="touqia" onclick="isHidden(touqia1)">投洽会专栏</li>
				<div id="touqia1" style="display:none">
				<li id="" onclick="isHidden(touqia0)">综合协调</li>	
				<div id="touqia0" style="display:none">
				<li id="TQ1-1">筹备进展</li>	
				<li id="TQ1-2">对外宣介材料</li>	
				<li id="TQ1-3">活动一览表</li>	
				<li id="TQ1-4">重要嘉宾</li>
				<li id="TQ1-5">往届材料</li>
				</div>
					

				<li id="" onclick="isHidden(touqia2)">活动筹备</li>	
				<div id="touqia2" style="display:none">
				<li id="TQ2-1">国际组织合作</li>	
				<li id="TQ2-2">四大IP招商板块</li>	
				<li id="TQ2-3">部办活动</li>	
				<li id="TQ2-4">其他活动</li>
				</div>
				<li id="TQ3">新闻宣传</li>	
				<li id="TQ4">信息及项目</li>	
				<li id="TQ5">会务保障</li>	
				<li id="TQ6">党建纪检</li>	
				<li id="TQ7">评估</li>	
				</div>
				</c:if>
			</ul>
		</div>
	</div>
	</c:if>
		
	<div class="menu">
		<div class="menu_title"  >常用功能</div>
		<div class="menu_items"  id="menu_items1">
			<ul>
				<!-- <li id="toPlatform">跨境产业平台<span id="count"></span></li> -->			
				<li id="toMessage">通知公告<span id="count" style="color:red;"></span></li>
				<li id="toMeeting">日程安排</li>
				<c:if test="${user.id == 1512 || user.id==135}">
				<li id="xiaojia"  >销假管理</li>
				</c:if>
				<c:if test="${user.deptId == 404}">
				<Li id="yusuan">预算统计</Li>
				<Li id="HTcount">合同一览</Li>
				<li id="toCurrency">外币录入</li>
				</c:if>
				
				<!-- <li id="toimpmeeting"  onclick="isHidden(impmeeting)">例会传达事项</li>
				<div id="impmeeting" style="display:none">
				<li id="towork">工作部署</li>
				<li id="tonotice">重要通知</li>
				<li id="todiscuss">专项议题</li>
				
					
				</div> -->
				
				<!--  <div id="xuanc" style="display:none">-->
				<!-- <li id="export">宣传口径导出<span id="count"></span></li> -->
				<!--<li id="edit">宣传口径录入<span id="count"></span></li>
				</div>-->
<%--  				<c:if test="${user.id == 135 }">
				 <li id="hetong">合同导出</li> 
				</c:if>  --%>
				<li id="dbsy" onclick="isHidden(event1)">待办（阅）事宜
  				 <span id="daiban" style="color:red;">
				<c:if test="${daibanCount != '' }">
					${daibanCount }
				</c:if>
				<c:if test="${daibanCount == '' }">
					<c:if test="${remindCount != '' }">
						${remindCount }
					</c:if>
				</c:if>
				</span></li>
				<div id="event1" style="display:none">
				 <li id="toRead">待阅事宜</li> 
				<!--<li id="event">待办事宜<span id="count"></span></li>-->
				<li id="event">待办事宜 <span id="approve" style="color:red;">
				<c:if test="${daibanCount != '' }">
					${daibanCount }
				</c:if> 
				</span></li> 
				<%-- <c:if test="${user.deptId != 429}">
				<li id="toRemind">回款提醒<span id="remind" style="color:red;">
				<c:if test="${remindCount != '' }">
					${remindCount }
				</c:if> 
				</span></li>
				</c:if> --%>
				</div>
				<li id="yiban"  >已办事宜</li>
				
				<%-- <c:if test="${ user.id!=1430   and user.id!=1427}"> <!-- 国际联络部 郁泽和方敏敏；会展部常晟和孙贤铭-->
				<li id="toWeekly">计划</li>
				</c:if> --%>


				<!-- <li id="ghtw">工会团委<span id="count"></span></li> -->

				<c:if test="${user.id != 1517 }">
				<li id="gwbl" onclick="isHidden(gwbl1)">公文办理</li>
				<div id="gwbl1" style="display:none">
				<li id="td0">公文课堂</li>
				<li id="gwtd"   onclick="isHidden(gwtd1)">公文套打</li>
				</div>
				<div id="gwtd1" style="display:none">
				
				<li id="td1">部内请示报告套打</li>
				<li id="td2">部内发文稿纸</li>
				<li id="td3">部内电报办理单</li>
				<li id="td4">部内文电处理</li>
				<li id="td5">机关发电文中</li>
				<li id="td6">机关发电文外</li>
				<li id="td7">局内函件套打</li>
				<li id="td8">党委函件套打</li>
				<li id="td9">白头文件报送处理单</li>
				<li id="td10">投资促进局流转签批单</li>
				</div>
				</c:if>
				<c:if test="${user.id == 1517 }">
				<li id="internalProcurementQD">渠道部门沟通机制</li>
				<li id="internalProcurementCY">产业部门内部采购机制公开表</li>
				</c:if>
				<c:if test="${user.id != 1486 and user.id != 1517 }">
				<li id="response">信息动态响应</li>
				
				<li id="achievementFile">已签署合同协议</li>
				
				<li id="internalProcurementQD">渠道部门沟通机制</li>
				<li id="internalProcurementCY">产业部门内部采购机制公开表</li>
				</c:if>
				<!-- <li id="cc_seach">公务接待缴纳费用统计台帐</li> -->
				<li id="juphonelist">局办公电话表</li>
				<c:if test="${user.id == 1336  || user.id==1512}">
				<li id="dutyoff">日程安排休假一览表</li>
				</c:if>


<!-- 				 <li id="jurole">规章制度</li>  -->

<%-- 				
				<c:if test="${user.deptId != 429 }">
				
					<li id="gongzhang">用章申请</li> 
					
					<c:if test="${user.id == 1417 }">
					<li id="gongzhangfinally">用章查询</li> 
					
					</c:if>
					<c:if test="${ user.id!=1430   and user.id!=1427}"> <!-- 国际联络部 郁泽和方敏敏；会展部常晟和孙贤铭-->
					<li id="lizhi">离职申请</li> 
					</c:if>
				</c:if>  --%>
				
				<c:if test="${user.id== 135 }">
				 <li id="updateLog">系统更新记录</li>
				
				 </c:if>
				 <c:if test="${ user.id==135   || user.id==1323  || user.id==1377 ||user.id==1451 ||user.id==1441 ||user.id==1256 || user.id==1260 || user.id==1417 ||user.id==1220}">
				  <li id="allxietong">岗位管理</li>

				 </c:if>
<%-- 				<c:if test="${user.id == 1235||user.id== 135 }">
				<!-- <li id="dbsy" onclick="isHidden(modular)">规章制度
  				</li> -->
				<div id="modular" style="display:none">
				
					<li id="addReg">局内规章制度录入/修改</li>
				
					<li id="toCatalogEntry">局内规章制度索引</li> 
				</c:if>
				</div> --%>
				
				<!-- <li id="message">消息列表</li>
				<li id="myAttention">我的关注<span id="count"></span></li>
				<li id="contact">通讯录</li>
				<li id="selfpage">个人信息修改</li>
				<li id="question">常见问题</li> -->				
				<%--<li id="calendar">日历</li>--%>				
				<%--<li id="content">内容管理</li>--%>				
				<%-- <li id="password">密码修改</li>--%>
				<%-- <li id="feedback">问题反馈</li>
	    		<li id="contactus">联系我们</li> --%>
			</ul>
		</div>
		
	</div>
	
	<c:if test="${user.id != 1486 and user.id != 1517}">
	
	<div class="menu"> 
		<div class="menu_title"  >审核事项</div>
		<div class="menu_items" style="" id="menu_items2">
			<ul>
						<div id="alldivTreeArea" class="main_content"> 
				<div id="divTree1"style="display:block"></div>
				<!-- <li id="toMeeting">行政人事党务<span id="count"></span></li> -->
				</div>
			</ul>
		</div>
	</div>
	
		</c:if>
	<c:if test="${ user.id!=1430   and user.id!=1427}"> <!-- 国际联络部 郁泽和方敏敏；会展部常晟和孙贤铭-->
	<div class="menu">
		<div class="menu_title"  >客户库</div>
		<div class="menu_items" id="menu_items3"> <!-- style="display:none" --> 
			<ul>
				<li id="tdcost">全局客户库</li>
				<li id="tdcost2">部门客户库</li>
				</div>
			</ul> 
	</div> 
	</c:if>
	<!--  
	<div class="menu">
		<div class="menu_title"  >投资促进局党务公开</div>
		<div class="menu_items" id="menu_items2"> <!-- style="display:none" 
			<ul>
				<li id="basic">基本情况</li>
				<li id="politics">政治建设情况</li>
				<li id="thought">思想建设情况</li>
				<li id="organization">组织建设情况</li>
				<li id="style">作风建设情况</li>
				<li id="discipline">纪律建设情况</li>
				<li id="system">制度建设情况</li>
				</div>
			</ul> 
	</div> -->
	
	<c:if test="${user.id == 135 }">
	<div class="menu">
		<div class="menu_title"  >管理员菜单</div>
		<div class="menu_items" id="menu_items3"> 
			<ul>
				<li id="signitem">会签岗位人员管理</li>
				</span>
				</div>
			</ul> 
	</div> 
	</c:if>	
	<%-- <div class="menu">
	    <div class="menu_title">
	    	常用下载
	    </div>
	    <div class="menu_items">
	    	<ul>
	    		<li id="downloads01">使用手册</li>
	    		
	    		<li id="addres">通讯录</li>
	    		
	    	</ul>
	    </div>
    </div> --%>
    </c:if>
</body>
</html>
